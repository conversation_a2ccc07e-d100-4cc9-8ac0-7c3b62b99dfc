runtime: nodejs20
service: frontend

# Environment variables for Next.js
env_variables:
  NODE_ENV: "production"
  NEXT_PUBLIC_API_BASE_URL: "https://propbolt.com"
  NEXT_PUBLIC_MAPBOX_TOKEN: "pk.eyJ1Ijoic2F2ZXVuZGVyY29udHJhY3QiLCJhIjoiY202anNoNWtvMDRpMjJqb2x1MWZ3ZGYxbCJ9.Bxl18yPka0BUSrGibMTbhg"
  NEXT_PUBLIC_REAL_ESTATE_API_KEY: "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
  NEXT_PUBLIC_REAL_ESTATE_API_URL: "https://api.realestateapi.com/v2/"
  NEXT_PUBLIC_APP_NAME: "Vacant Land Search"
  NEXT_PUBLIC_APP_DESCRIPTION: "Professional real estate admin panel for vacant land search in Daytona Beach, Florida"
  DEMO_MODE: "false"
  PORT: "8080"

# Automatic scaling configuration
automatic_scaling:
  min_instances: 1
  max_instances: 5
  target_cpu_utilization: 0.6

# Resource allocation
resources:
  cpu: 1
  memory_gb: 1

# Handlers for Next.js static files and pages
handlers:
# Static files from .next/static
- url: /_next/static
  static_dir: .next/static
  secure: always

# Static files from public directory
- url: /favicon.ico
  static_files: public/favicon.ico
  upload: public/favicon.ico
  secure: always

- url: /(.+\.(png|jpg|jpeg|gif|svg|ico|css|js|woff|woff2|ttf|eot))$
  static_files: public/\1
  upload: public/.*\.(png|jpg|jpeg|gif|svg|ico|css|js|woff|woff2|ttf|eot)$
  secure: always

# All other requests go to Next.js
- url: /.*
  script: auto
  secure: always

# Skip files that shouldn't be uploaded
skip_files:
- ^(.*/)?#.*#$
- ^(.*/)?.*~$
- ^(.*/)?.*\.py[co]$
- ^(.*/)?.*/RCS/.*$
- ^(.*/)?\..*$
- ^(.*/)?node_modules/.*$
- ^(.*/)?\.next/cache/.*$
- ^(.*/)?\.git/.*$
- ^(.*/)?\.gitignore$
- ^(.*/)?README\..*$
- ^(.*/)?\.env\..*$
- ^(.*/)?\.history/.*$
