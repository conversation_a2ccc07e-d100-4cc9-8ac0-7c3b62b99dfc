# Frontend-specific .gcloudignore for Google App Engine deployment
# This file is specifically designed to minimize file count for Next.js deployment

# Node.js dependencies - CRITICAL to exclude
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Next.js cache and development files
.next/cache/
.next/trace
.next/build-manifest.json
.next/export-marker.json
.next/prerender-manifest.json
.next/routes-manifest.json
.next/images-manifest.json
out/
build/
dist/

# Environment files
.env*

# Development tools
.vscode/
.idea/
.history/
.internal/
*.swp
*.swo

# Version control
.git/
.gitignore

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# TypeScript and build artifacts
*.tsbuildinfo
.eslintcache

# Configuration files not needed in production
tsconfig.json
next.config.js
tailwind.config.js
postcss.config.js
next-env.d.ts

# Go files (not needed for frontend service)
*.go
go.mod
go.sum
propbolt
*.exe
*.test
*.out

# Development directories
devtesting/
scripts/

# Documentation
README.md
*.md
platform*.png

# Deployment scripts
deploy.sh
start-*.sh
Procfile
*.yaml
!frontend-app.yaml

# Public files not needed
public/curl_input.html
public/dashboard.html
public/tutorials-one.html

# Temporary files
pids/
*.pid
*.seed
*.pid.lock
coverage/
*.lcov
