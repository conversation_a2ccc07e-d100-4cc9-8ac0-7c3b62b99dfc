# This file specifies files that are *not* uploaded to Google Cloud
# using gcloud. It follows the same syntax as .gitignore, with the addition of
# "#!include" directives (which insert the entries of the given .gitignore-style
# file at that point).
#
# For more information, run:
#   $ gcloud topic gcloudignore
#
.gcloudignore
# If you would like to upload your .git directory, .gitignore file or files
# from your .gitignore file, remove the corresponding line
# below:
.git
.gitignore

# Node.js dependencies and development files
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Next.js development and cache files
.next/cache/
.next/trace
.next/build-manifest.json
.next/export-marker.json
.next/prerender-manifest.json
.next/routes-manifest.json
.next/images-manifest.json
out/
build/
dist/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Development tools and configs
.vscode/
.idea/
*.swp
*.swo
.history/
.internal/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# TypeScript
*.tsbuildinfo

# ESLint
.eslintcache

# Go binaries and test files
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work
propbolt

# Development and testing directories
devtesting/
scripts/
public/curl_input.html
public/dashboard.html
public/tutorials-one.html

# Documentation and project files
README.md
*.md
migration_log.md
complete_migration.sh
migrate_domain.sh
deploy-frontend.sh
platform.png
platform2.png

# Configuration files not needed in production
.gitignore
tsconfig.json
next.config.js
tailwind.config.js
postcss.config.js
next-env.d.ts

# Deployment scripts and configs
deploy.sh
start-dev.sh
start-full-stack.sh
Procfile
1.yaml
transaction.yaml