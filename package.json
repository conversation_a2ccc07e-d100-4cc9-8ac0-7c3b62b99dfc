{"name": "vacant-land-search-nextjs", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p ${PORT:-8080}", "start:prod": "next start -p 8080", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^5.3.3", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "mapbox-gl": "^3.0.1", "@types/mapbox-gl": "^2.7.19", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "class-variance-authority": "^0.7.0", "date-fns": "^2.30.0", "@headlessui/react": "^1.7.17", "framer-motion": "^10.16.16"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "@tailwindcss/forms": "^0.5.7"}}